# Panduan Pemilihan Kategori Berdasarkan Fungsi dan POV

## Konsep Utama

Sistem telah diperbarui untuk memilih kategori berdasar<PERSON> **FUNGSI** dan **PURPOSE** dari gambar/video, bukan hanya objek yang terlihat.

## Daftar Kategori yang Benar

### Video Categories (19 kategori):
Animals/Wildlife, Arts, Backgrounds/Textures, Buildings/Landmarks, Business/Finance, Education, Food and drink, Healthcare/Medical, Holidays, Industrial, Nature, Objects, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation

### Image Categories (26 kategori):
Abstract, Animals/Wildlife, Arts, Backgrounds/Textures, Beauty/Fashion, Buildings/Landmarks, Business/Finance, Celebrities, Education, Food and drink, Healthcare/Medical, Holidays, Industrial, Interiors, Miscellaneous, Nature, Objects, Parks/Outdoor, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation, Vintage

## Instruksi untuk Gemini

### Prinsip Pemilihan:
1. **Analisis <PERSON>** - A<PERSON> tujuan/kegunaan utama dari konten ini?
2. **Konteks Penggunaan** - Bagaimana konten ini akan digunakan?
3. **POV (Point of View)** - Dari sudut pandang apa konten ini dibuat?

## Perbedaan Kategori Video vs Gambar

### Kategori Khusus untuk Gambar (tidak ada di video):
- **Abstract** - Gambar abstrak, konseptual
- **Beauty/Fashion** - Mode, kecantikan, gaya hidup
- **Celebrities** - Tokoh terkenal, selebriti
- **Interiors** - Desain interior, ruangan
- **Miscellaneous** - Kategori campuran
- **Parks/Outdoor** - Taman, aktivitas luar ruangan
- **Vintage** - Gaya retro, klasik

### Kategori yang Sama untuk Video dan Gambar:
Animals/Wildlife, Arts, Backgrounds/Textures, Buildings/Landmarks, Business/Finance, Education, Food and drink, Healthcare/Medical, Holidays, Industrial, Nature, Objects, People, Religion, Science, Signs/Symbols, Sports/Recreation, Technology, Transportation

## Contoh Pemilihan yang Benar

### Gambar Teknologi:
- **Salah:** Foto laptop → "Objects"
- **Benar:** Foto laptop → "Technology" atau "Business/Finance" (tergantung konteks)

### Gambar Makanan:
- **Salah:** Foto makanan dengan piring → "Objects"
- **Benar:** Foto makanan → "Food and drink" (fokus pada makanan, bukan piring)

### Video Bisnis:
- **Salah:** Video meeting dengan meja → "Objects"
- **Benar:** Video meeting → "Business/Finance" (fokus pada aktivitas bisnis)

### Gambar Alam:
- **Salah:** Foto pohon → "Objects"
- **Benar:** Foto pohon → "Nature" atau "Parks/Outdoor" (tergantung konteks)

### Video Edukasi:
- **Salah:** Video tutorial dengan komputer → "Objects" atau "Technology"
- **Benar:** Video tutorial → "Education" (fokus pada fungsi mengajar)

## Kategori Berdasarkan Fungsi

### Business/Finance
- Konten untuk presentasi bisnis
- Materi pemasaran
- Dokumentasi korporat
- Meeting dan diskusi bisnis

### Technology
- Demonstrasi produk teknologi
- Tutorial software/hardware
- Inovasi dan pengembangan
- Digital transformation

### Education
- Materi pembelajaran
- Tutorial dan panduan
- Presentasi akademik
- Konten edukasi

### Food and drink
- Resep dan memasak
- Review makanan
- Dokumentasi kuliner
- Promosi restoran

### Healthcare/Medical
- Edukasi kesehatan
- Dokumentasi medis
- Promosi layanan kesehatan
- Wellness dan fitness

### Nature
- Dokumentasi lingkungan
- Konten konservasi
- Wisata alam
- Edukasi ekologi

## Implementasi dalam Prompt

```javascript
// Contoh instruksi yang ditambahkan ke prompt:
"Analyze the PRIMARY FUNCTION and PURPOSE of this content, then select categories. 
Focus on what the content is USED FOR or its INTENDED PURPOSE, not just what objects are visible.

Examples:
- A photo of a laptop should be 'Technology' or 'Business/Finance' based on its context, not 'Objects'
- A food photo should be 'Food and drink' regardless of plates/utensils shown
- A cooking video should be 'Food and drink' regardless of kitchen tools shown"
```

## Hasil yang Diharapkan

### Sebelum (Berdasarkan Objek):
- Foto laptop → "Objects"
- Video memasak → "Objects" (karena fokus pada peralatan)
- Foto meeting → "People" (karena fokus pada orang)

### Sesudah (Berdasarkan Fungsi):
- Foto laptop → "Technology" atau "Business/Finance"
- Video memasak → "Food and drink"
- Foto meeting → "Business/Finance"

## Testing

Untuk menguji implementasi:
1. Upload gambar/video dengan objek yang jelas
2. Perhatikan apakah kategori dipilih berdasarkan fungsi, bukan objek
3. Verifikasi bahwa konteks dan tujuan penggunaan dipertimbangkan

## Status: ✅ IMPLEMENTED

Perubahan telah diterapkan di:
- `js/api.js` - Prompt untuk gambar
- `js/gemini-video-api.js` - Prompt untuk video

Sistem sekarang akan memilih kategori berdasarkan fungsi dan POV, bukan hanya objek yang terlihat.
